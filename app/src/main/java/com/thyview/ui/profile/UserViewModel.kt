package com.thyview.ui.profile

import androidx.lifecycle.ViewModel
import androidx.lifecycle.viewModelScope
import com.thyview.models.*
import com.thyview.repositories.UserRepository
import com.thyview.services.AuthService
import dagger.hilt.android.lifecycle.HiltViewModel
import kotlinx.coroutines.flow.MutableStateFlow
import kotlinx.coroutines.flow.StateFlow
import kotlinx.coroutines.flow.asStateFlow
import kotlinx.coroutines.launch
import timber.log.Timber
import javax.inject.Inject

/**
 * ViewModel for user-related operations
 */
@HiltViewModel
class UserViewModel @Inject constructor(
    private val userRepository: UserRepository
) : ViewModel() {

    private val _currentUser = MutableStateFlow<User?>(null)
    val currentUser: StateFlow<User?> = _currentUser.asStateFlow()

    private val _isLoading = MutableStateFlow(false)
    val isLoading: StateFlow<Boolean> = _isLoading.asStateFlow()

    private val _error = MutableStateFlow<String?>(null)
    val error: StateFlow<String?> = _error.asStateFlow()

    private val _usernameAvailable = MutableStateFlow<Boolean?>(null)
    val usernameAvailable: StateFlow<Boolean?> = _usernameAvailable.asStateFlow()

    /**
     * Create a new user with Firebase Auth data
     */
    fun createUser() {
        viewModelScope.launch {
            _isLoading.value = true
            _error.value = null

            try {
                val email = AuthService.getCurrentUserEmail()
                val name = AuthService.getCurrentUserDisplayName()
                val profileImageUrl = AuthService.getCurrentUserPhotoUrl()
                val region = AuthService.getDeviceRegion()

                if (email != null && name != null) {
                    val request = CreateUserRequest(
                        email = email,
                        name = name,
                        profileImageUrl = profileImageUrl,
                        region = region
                    )

                    val result = userRepository.createUser(request)
                    result.fold(
                        onSuccess = { user ->
                            _currentUser.value = user
                            Timber.d("User created successfully: ${user.username}")
                        },
                        onFailure = { exception ->
                            _error.value = "Failed to create user: ${exception.message}"
                            Timber.e(exception, "Failed to create user")
                        }
                    )
                } else {
                    _error.value = "Missing required user information from Firebase Auth"
                }
            } catch (e: Exception) {
                _error.value = "Unexpected error: ${e.message}"
                Timber.e(e, "Unexpected error during user creation")
            } finally {
                _isLoading.value = false
            }
        }
    }

    /**
     * Get current user profile
     */
    fun getCurrentUser() {
        viewModelScope.launch {
            _isLoading.value = true
            _error.value = null

            val result = userRepository.getCurrentUser()
            result.fold(
                onSuccess = { user ->
                    _currentUser.value = user
                },
                onFailure = { exception ->
                    _error.value = "Failed to get user profile: ${exception.message}"
                    Timber.e(exception, "Failed to get current user")
                }
            )
            _isLoading.value = false
        }
    }

    /**
     * Refresh current user profile (useful after profile image updates)
     */
    fun refreshCurrentUser() {
        getCurrentUser()
    }

    /**
     * Update user profile
     */
    fun updateUser(request: UpdateUserRequest) {
        viewModelScope.launch {
            _isLoading.value = true
            _error.value = null

            val result = userRepository.updateUser(request)
            result.fold(
                onSuccess = { user ->
                    _currentUser.value = user
                    Timber.d("User updated successfully")
                },
                onFailure = { exception ->
                    _error.value = "Failed to update user: ${exception.message}"
                    Timber.e(exception, "Failed to update user")
                }
            )
            _isLoading.value = false
        }
    }

    /**
     * Check username availability
     */
    fun checkUsernameAvailability(username: String) {
        viewModelScope.launch {
            _error.value = null

            val result = userRepository.checkUsernameAvailability(username)
            result.fold(
                onSuccess = { available ->
                    _usernameAvailable.value = available
                },
                onFailure = { exception ->
                    _error.value = "Failed to check username availability: ${exception.message}"
                    _usernameAvailable.value = null
                    Timber.e(exception, "Failed to check username availability")
                }
            )
        }
    }

    /**
     * Delete current user
     */
    fun deleteUser() {
        viewModelScope.launch {
            _isLoading.value = true
            _error.value = null

            val result = userRepository.deleteUser()
            result.fold(
                onSuccess = { response ->
                    _currentUser.value = null
                    Timber.d("User deleted successfully: ${response.message}")
                },
                onFailure = { exception ->
                    _error.value = "Failed to delete user: ${exception.message}"
                    Timber.e(exception, "Failed to delete user")
                }
            )
            _isLoading.value = false
        }
    }

    /**
     * Clear error state
     */
    fun clearError() {
        _error.value = null
    }

    /**
     * Clear username availability state
     */
    fun clearUsernameAvailability() {
        _usernameAvailable.value = null
    }


}
